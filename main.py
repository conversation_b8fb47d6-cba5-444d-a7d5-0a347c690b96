from htra_api import *
import os
import struct
import matplotlib.pyplot as plt
import numpy as np
import msgpack


# 字节序转换函数，对应C++中的swap函数
def swap_16(val):
    return ((val & 0x00ff) << 8) | ((val & 0xff00) >> 8)


def swap_32(val):
    return ((val & 0xff000000) >> 24) | \
           ((val & 0x00ff0000) >> 8) | \
           ((val & 0x0000ff00) << 8) | \
           ((val & 0x000000ff) << 24)


def swap_64(value):
    result = 0
    result |= (value & 0xff00000000000000) >> 56
    result |= (value & 0x00ff000000000000) >> 40
    result |= (value & 0x0000ff0000000000) >> 24
    result |= (value & 0x000000ff00000000) >> 8
    result |= (value & 0x00000000ff000000) << 8
    result |= (value & 0x0000000000ff0000) << 24
    result |= (value & 0x000000000000ff00) << 40
    result |= (value & 0x00000000000000ff) << 56
    return result


# float类型字节序转换
def convert_endian_float(value_bytes):
    # 先将bytes转为int32
    val_int = struct.unpack('<I', value_bytes)[0]
    # 进行字节序转换
    val_int_swapped = swap_32(val_int)
    # 将转换后的int32转回float
    return struct.unpack('!f', struct.pack('!I', val_int_swapped))[0]


# double类型字节序转换
def convert_endian_double(value_bytes):
    # 先将bytes转为int64
    val_int = struct.unpack('<Q', value_bytes)[0]
    # 进行字节序转换
    val_int_swapped = swap_64(val_int)
    # 将转换后的int64转回double
    return struct.unpack('!d', struct.pack('!Q', val_int_swapped))[0]


# 解析msgpack数据，类似于C++中的msgpack_unpack_next
def unpack_msgpack_data(data):
    unpacker = msgpack.Unpacker()
    unpacker.feed(data)
    return list(unpacker)


# 获取IQS模式下的文件信息，对应C++中的GetIQSWavFileInfo1函数
def get_iqs_wav_file_info(file_path):
    status = 0
    
    # 打开WAV文件
    try:
        with open(file_path, 'rb') as file:
            # 读取IQS_Profile和IQS_StreamInfo结构体大小
            file.seek(108)
            struct_size_bytes = file.read(2)
            struct_size = swap_16(struct.unpack('<H', struct_size_bytes)[0])
            
            # 读取IQS_Profile和IQS_StreamInfo数据
            iqs_profile_stream_data = file.read(struct_size)
            
            # 使用msgpack解析数据，与C++实现类似
            try:
                # 解析二进制数据
                unpacked_data = unpack_msgpack_data(iqs_profile_stream_data)
                
                # 创建IQS_Profile和IQS_StreamInfo结构体
                iqs_profile = IQS_Profile_TypeDef()
                iqs_stream_info = IQS_StreamInfo_TypeDef()
                
                # 按照C++中的顺序解析数据
                # 第1个值是CenterFreq_Hz (double)
                center_freq = unpacked_data[0]
                
                # 第2个值是RefLevel_dBm (double)
                ref_level = unpacked_data[1]
                
                # 第3个值是DecimateFactor (uint32_t)
                decimate_factor = int(unpacked_data[2])
                
                # 跳过其他值...
                
                # 第38个值是Bandwidth (double)
                bandwidth = unpacked_data[37] if len(unpacked_data) > 37 else 0.0
                
                # 第39个值是IQSampleRate (double)
                # 原始代码：sample_rate = unpacked_data[38] if len(unpacked_data) > 38 else 0.0
                # 修改后的代码：确保采样率单位正确，可能需要转换
                if len(unpacked_data) > 38:
                    raw_sample_rate = unpacked_data[38]
                    # 检查采样率值是否合理，如果过小则可能需要转换单位
                    if raw_sample_rate < 1.0:  # 如果采样率小于1Hz，可能是单位问题
                        sample_rate = raw_sample_rate * 1000000  # 转换为Hz (假设原始值是MHz)
                    else:
                        sample_rate = raw_sample_rate
                else:
                    # 默认采样率，如果无法从文件中读取
                    sample_rate = 10000000.0  # 10MHz是常见的IQ采样率
                
                # 将解析出的值赋给结构体
                iqs_profile.CenterFreq_Hz = center_freq
                iqs_profile.DecimateFactor = decimate_factor
                iqs_stream_info.IQSampleRate = sample_rate
                iqs_stream_info.Bandwidth = bandwidth
                
            except Exception as e:
                print(f"Error parsing msgpack data: {e}")
                # 如果解析失败，使用默认值
                center_freq = 2450000000.0
                sample_rate = 10000000.0
                decimate_factor = 1
            
            # 读取DeviceInfo
            device_info = DeviceInfo_TypeDef()
            
            file.read(2)  # DeviceInfo结构体字节长度
            
            # 读取设备UID
            device_uid_bytes = file.read(8)
            device_info.DeviceUID = swap_64(struct.unpack('<Q', device_uid_bytes)[0])
            
            # 读取设备型号
            model_bytes = file.read(2)
            device_info.Model = swap_16(struct.unpack('<H', model_bytes)[0])
            
            # 读取设备硬件版本
            hw_version_bytes = file.read(2)
            device_info.HardwareVersion = swap_16(struct.unpack('<H', hw_version_bytes)[0])
            
            # 读取设备MCU固件版本
            mfw_version_bytes = file.read(4)
            device_info.MFWVersion = swap_32(struct.unpack('<I', mfw_version_bytes)[0])
            
            # 读取设备FPGA固件版本
            ffw_version_bytes = file.read(4)
            device_info.FFWVersion = swap_32(struct.unpack('<I', ffw_version_bytes)[0])
            
            # 读取IQ数据总长度
            file.seek(25 * 1024 * 1024 + 404)
            iq_size_bytes = file.read(4)
            iq_size = struct.unpack('<I', iq_size_bytes)[0]
            
            # 计算数据包数量
            packet_count = iq_size // 64968
            
            return {
                'IQS_Profile': iqs_profile,
                'IQS_StreamInfo': iqs_stream_info,
                'DeviceInfo': device_info,
                'PacketCount': packet_count,
                'IQSize': iq_size,
                'CenterFreq_Hz': center_freq,
                'IQSampleRate': sample_rate,
                'DecimateFactor': decimate_factor
            }
    except Exception as e:
        status = -1009
        print(f"Error reading file: {e}")
        return None
    
    return None


# 获取IQS模式下的数据，对应C++中的GetIQSWavFileData1函数
def get_iqs_wav_file_data(file_path, iqs_profile, packet_num):
    status = 0
    
    try:
        with open(file_path, 'rb') as file:
            # 创建IQStream结构体
            iq_stream = IQStream_TypeDef()
            
            # 读取IQS_TriggerInfo
            file.seek(408 + packet_num * 405)
            file.read(2)  # IQS_TriggerInfo Struct Size
            
            # 读取SysTimerCountOfFirstDataPoint
            sys_timer_bytes = file.read(8)
            sys_timer = convert_endian_double(sys_timer_bytes)
            
            # 读取InPacketTriggeredDataSize
            triggered_data_size_bytes = file.read(2)
            triggered_data_size = swap_16(struct.unpack('<H', triggered_data_size_bytes)[0])
            
            # 读取InPacketTriggerEdges
            trigger_edges_bytes = file.read(2)
            trigger_edges = swap_16(struct.unpack('<H', trigger_edges_bytes)[0])
            
            # 跳过其他触发信息
            file.seek(408 + packet_num * 405 + 2 + 8 + 2 + 2 + 25 * 4 + 25 * 8 + 25 * 1)
            
            # 读取DeviceState
            file.read(2)  # DeviceState struct size
            
            # 读取Temperature
            temp_bytes = file.read(2)
            temperature = swap_16(struct.unpack('<H', temp_bytes)[0]) * 0.01  # 摄氏度 = 0.01 * Temperature
            
            # 读取RF状态和BB状态
            rf_state_bytes = file.read(2)
            rf_state = swap_16(struct.unpack('<H', rf_state_bytes)[0])
            
            bb_state_bytes = file.read(2)
            bb_state = swap_16(struct.unpack('<H', bb_state_bytes)[0])
            
            # 读取AbsoluteTimeStamp (GNSS时间戳)
            abs_timestamp_bytes = file.read(8)
            abs_timestamp = convert_endian_double(abs_timestamp_bytes)

            # 读取Latitude (纬度)
            latitude_bytes = file.read(4)
            latitude = convert_endian_float(latitude_bytes)

            # 读取Longitude (经度)
            longitude_bytes = file.read(4)
            longitude = convert_endian_float(longitude_bytes)

            # 跳过其他DeviceState字段
            file.seek(408 + packet_num * 405 + 2 + 8 + 2 + 2 + 25 * 4 + 25 * 8 + 25 * 1 + 2 + 2 + 2 + 2 + 8 + 4 + 4 + 2 + 8 + 4 + 4 + 4 + 2 + 2 + 2 + 2)
            
            # 读取IQS_ScaleToV
            scale_to_v_bytes = file.read(4)
            scale_to_v = convert_endian_float(scale_to_v_bytes)
            
            # 读取MaxPower_dBm
            max_power_bytes = file.read(4)
            max_power = convert_endian_float(max_power_bytes)
            
            # 读取MaxIndex
            max_index_bytes = file.read(4)
            max_index = swap_32(struct.unpack('<I', max_index_bytes)[0])
            
            # 读取IQ数据
            file.seek(25 * 1024 * 1024 + 408 + packet_num * 64968)
            
            # 判断数据格式 - 使用DataFormat_TypeDef的枚举值
            format_size = 2  # 默认为Complex16bit (0x00)
            if hasattr(iqs_profile, 'DataFormat'):
                if iqs_profile.DataFormat == 0x01:  # Complex32bit
                    format_size = 4
                elif iqs_profile.DataFormat == 0x02:  # Complex8bit
                    format_size = 1
            
            # 计算样本数
            samples = 64968 // format_size // 2  # 每个IQ对占用2*format_size字节
            
            # 读取IQ数据 - 修改为与C++相同的读取方式
            iq_data = []
            for i in range(samples):
                # 注意：C++中是先读Q后读I，所以这里要保持一致
                q_bytes = file.read(2)
                i_bytes = file.read(2)
                q_val = struct.unpack('<h', q_bytes)[0]
                i_val = struct.unpack('<h', i_bytes)[0]
                # 存储为(Q, I)对，与C++保持一致
                iq_data.append((q_val, i_val))
            
            return {
                'SysTimerCount': sys_timer,
                'Temperature': temperature,
                'RFState': rf_state,
                'BBState': bb_state,
                'ScaleToV': scale_to_v,
                'MaxPower_dBm': max_power,
                'MaxIndex': max_index,
                'AbsoluteTimeStamp': abs_timestamp,
                'Latitude': latitude,
                'Longitude': longitude,
                'IQData': iq_data
            }
    except Exception as e:
        status = -1009
        print(f"Error reading file data: {e}")
        return None
    
    return None


# 将WAV文件转换为CSV文件，对应C++中的IQSMode_WavToCsv函数
def wav_to_csv(wav_file_path, csv_file_path):
    # 读取文件信息
    file_info = get_iqs_wav_file_info(wav_file_path)
    if not file_info:
        print("Failed to get file information")
        return None
    
    # 创建CSV文件
    with open(csv_file_path, 'w') as csv_file:
        # 写入设备信息
        csv_file.write(f"Device UID:,{hex(file_info['DeviceInfo'].DeviceUID)}\n")
        
        # 写入中心频率、采样率和抽取因子
        csv_file.write(f"CenterFreq_Hz:,{file_info['CenterFreq_Hz']}\n")
        csv_file.write(f"IQSampleRate:,{file_info['IQSampleRate']}\n")
        csv_file.write(f"DecimateFactor:,{file_info['DecimateFactor']}\n")
        csv_file.write("I_Data,Q_Data\n")
        
        # 创建单独的I和Q数组，与C++实现保持一致
        if 'IQS_StreamInfo' in file_info and hasattr(file_info['IQS_StreamInfo'], 'PacketSamples'):
            packet_samples = file_info['IQS_StreamInfo'].PacketSamples
        else:
            # 假设每个数据包有16242个样本（64968字节 / 4字节每样本）
            packet_samples = 64968 // 4
            
        i_data = []
        q_data = []
        
        # 读取所有数据包的IQ数据
        all_iq_data = []
        for packet_num in range(file_info['PacketCount']):
            packet_data = get_iqs_wav_file_data(wav_file_path, file_info['IQS_Profile'], packet_num)
            if not packet_data:
                print(f"Failed to get packet data for packet {packet_num}")
                continue
            
            iq_data = packet_data['IQData']
            
            # 确定这个包中需要处理的点数
            points = len(iq_data)
            if packet_num == file_info['PacketCount'] - 1:
                # 如果是最后一个包，可能不是满的
                # 这里简化处理，实际应该根据StreamSamples计算
                pass
            
            # 分离Q和I数据，与C++保持一致
            for q, i in iq_data[:points]:
                q_data.append(q)
                i_data.append(i)
                # 注意：在C++中，CSV写入的顺序是I,Q，所以这里也保持一致
                csv_file.write(f"{i},{q}\n")
            
            # 收集数据用于可能的后续处理
            all_iq_data.extend(iq_data[:points])
    
    # 返回分离的I和Q数据，而不是IQ对
    return i_data, q_data


# 提取GNSS信息的专门函数
def extract_gnss_info(wav_file_path):
    """
    从WAV文件中提取GNSS信息（经纬度和时间戳）
    """
    # 读取文件信息
    file_info = get_iqs_wav_file_info(wav_file_path)
    if not file_info:
        print("Failed to get file information")
        return None

    gnss_data = []

    # 读取每个数据包的GNSS信息
    for packet_num in range(file_info['PacketCount']):
        packet_data = get_iqs_wav_file_data(wav_file_path, file_info['IQS_Profile'], packet_num)
        if not packet_data:
            print(f"Failed to get packet data for packet {packet_num}")
            continue

        gnss_info = {
            'PacketNumber': packet_num,
            'AbsoluteTimeStamp': packet_data['AbsoluteTimeStamp'],
            'Latitude': packet_data['Latitude'],
            'Longitude': packet_data['Longitude'],
            'SysTimerCount': packet_data['SysTimerCount']
        }
        gnss_data.append(gnss_info)

    return gnss_data


def main():
    # 使用示例
    wav_file_path = r"D:\nxm-60\Windows\SAStudio4\data\0053_20250723_202435.part1.iq.wav"
    csv_file_path = "./data/IQSMode_Data_new.csv"  # 修改文件名，避免权限冲突
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(csv_file_path), exist_ok=True)

    # 首先提取并显示GNSS信息
    print("正在提取GNSS信息...")
    gnss_data = extract_gnss_info(wav_file_path)

    if gnss_data:
        print(f"\n找到 {len(gnss_data)} 个数据包的GNSS信息:")
        print("-" * 80)
        print(f"{'包号':<6} {'时间戳':<20} {'纬度':<12} {'经度':<12} {'系统计时器':<15}")
        print("-" * 80)

        for gnss in gnss_data:
            print(f"{gnss['PacketNumber']:<6} "
                  f"{gnss['AbsoluteTimeStamp']:<20.6f} "
                  f"{gnss['Latitude']:<12.6f} "
                  f"{gnss['Longitude']:<12.6f} "
                  f"{gnss['SysTimerCount']:<15.6f}")

        # 保存GNSS信息到CSV文件
        gnss_csv_path = "./data/gnss_info.csv"
        with open(gnss_csv_path, 'w') as f:
            f.write("PacketNumber,AbsoluteTimeStamp,Latitude,Longitude,SysTimerCount\n")
            for gnss in gnss_data:
                f.write(f"{gnss['PacketNumber']},{gnss['AbsoluteTimeStamp']},{gnss['Latitude']},{gnss['Longitude']},{gnss['SysTimerCount']}\n")
        print(f"\nGNSS信息已保存到: {gnss_csv_path}")
    else:
        print("未能提取到GNSS信息")

    # 转换WAV到CSV
    result = wav_to_csv(wav_file_path, csv_file_path)
    
    if result:
        i_data, q_data = result
        print(f"转换完成，数据已保存到 {csv_file_path}")
        
        # 可以进行额外的数据可视化
        
        # 添加前10000个点的时域图
        plt.figure(figsize=(12, 8))
        time_points = np.arange(10000)
        plt.plot(time_points, i_data[:10000], label='I Channel')
        plt.plot(time_points, q_data[:10000], label='Q Channel')
        plt.title('Time Domain Plot (first 10000 samples)')
        plt.xlabel('Sample Index')
        plt.ylabel('Amplitude')
        plt.legend()
        plt.grid(True)
        plt.savefig('./data/time_domain_10000.png')
        plt.close()
        
        plt.figure(figsize=(10, 6))
        plt.subplot(2, 1, 1)
        plt.plot(i_data[:1000])
        plt.title('I Channel (first 1000 samples)')
        plt.subplot(2, 1, 2)
        plt.plot(q_data[:1000])
        plt.title('Q Channel (first 1000 samples)')
        plt.tight_layout()
        plt.savefig('./data/iq_plot.png')
        plt.close()
        
        # 绘制单个采样点的星座图
        # 选择一些特定的采样点进行展示
        sample_indices = [100, 500, 1000, 2000, 5000, 10000, 20000]
        plt.figure(figsize=(10, 10))
        
        # 绘制所有点作为背景（灰色、半透明）
        plt.scatter(i_data[:50000], q_data[:50000], s=0.5, alpha=0.1, color='gray', label='Background Points')
        
        # 绘制选定的单个点（彩色、大尺寸）
        colors = ['red', 'blue', 'green', 'purple', 'orange', 'cyan', 'magenta']
        for idx, sample_idx in enumerate(sample_indices):
            if sample_idx < len(i_data):
                plt.scatter(i_data[sample_idx], q_data[sample_idx], s=50, color=colors[idx % len(colors)], 
                           label=f'Sample #{sample_idx}', edgecolors='black')
        
        plt.title('IQ Constellation - Selected Individual Samples')
        plt.xlabel('I')
        plt.ylabel('Q')
        plt.grid(True)
        plt.axis('equal')
        plt.legend()
        plt.savefig('./data/constellation_individual_samples.png')
        plt.close()
        
        # IQ星座图 - 显示所有采样点
        plt.figure(figsize=(8, 8))
        
        # 如果数据点太多，可能会导致绘图缓慢或内存不足，可以考虑采样
        total_points = len(i_data)
        print(f"总采样点数: {total_points}")
        
        # 如果点数太多，可以考虑降采样，但这里我们尝试绘制所有点
        try:
            plt.scatter(i_data, q_data, s=0.5, alpha=0.3)
            plt.title(f'IQ Constellation (All {total_points} samples)')
        except MemoryError:
            # 如果内存不足，则降采样
            sample_rate = max(1, total_points // 100000)  # 最多绘制约10万个点
            plt.scatter(i_data[::sample_rate], q_data[::sample_rate], s=0.5, alpha=0.3)
            plt.title(f'IQ Constellation (Sampled from {total_points} points, every {sample_rate}th point)')
        
        plt.xlabel('I')
        plt.ylabel('Q')
        plt.grid(True)
        plt.axis('equal')
        plt.savefig('./data/constellation_all.png')
        plt.close()
    else:
        print("转换失败")


if __name__ == "__main__":
    main()