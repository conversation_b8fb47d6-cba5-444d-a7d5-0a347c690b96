#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳分析脚本
分析IQS WAV文件中的特殊时间戳格式
"""

from datetime import datetime, timezone
import struct

def analyze_timestamp(timestamp):
    """
    分析时间戳的可能格式
    
    Args:
        timestamp: 原始时间戳值
    """
    print(f"原始时间戳: {timestamp}")
    print(f"时间戳类型: {type(timestamp)}")
    print(f"时间戳长度: {len(str(int(timestamp)))} 位")
    
    # 尝试不同的解释方式
    print("\n可能的时间戳格式分析:")
    print("-" * 50)
    
    # 1. 直接作为Unix时间戳（秒）
    try:
        dt1 = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        print(f"1. Unix时间戳（秒）: {dt1.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"1. Unix时间戳（秒）: 失败 - {e}")
    
    # 2. 毫秒级时间戳
    try:
        dt2 = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
        print(f"2. 毫秒级时间戳: {dt2.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"2. 毫秒级时间戳: 失败 - {e}")
    
    # 3. 微秒级时间戳
    try:
        dt3 = datetime.fromtimestamp(timestamp / 1000000, tz=timezone.utc)
        print(f"3. 微秒级时间戳: {dt3.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"3. 微秒级时间戳: 失败 - {e}")
    
    # 4. 纳秒级时间戳
    try:
        dt4 = datetime.fromtimestamp(timestamp / 1000000000, tz=timezone.utc)
        print(f"4. 纳秒级时间戳: {dt4.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"4. 纳秒级时间戳: 失败 - {e}")
    
    # 5. 100纳秒级时间戳（Windows FILETIME格式）
    try:
        # Windows FILETIME: 从1601年1月1日开始的100纳秒间隔数
        # 转换为Unix时间戳需要减去1601到1970的差值
        windows_epoch_diff = 11644473600  # 1601到1970的秒数
        dt5 = datetime.fromtimestamp((timestamp / 10000000) - windows_epoch_diff, tz=timezone.utc)
        print(f"5. Windows FILETIME: {dt5.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"5. Windows FILETIME: 失败 - {e}")
    
    # 6. 系统特定的高精度计时器
    print(f"\n6. 可能的系统计时器分析:")
    print(f"   - 如果是系统启动后的计时器（毫秒）: {timestamp / 1000:.3f} 秒")
    print(f"   - 如果是系统启动后的计时器（微秒）: {timestamp / 1000000:.6f} 秒")
    print(f"   - 如果是系统启动后的计时器（纳秒）: {timestamp / 1000000000:.9f} 秒")
    
    # 7. 分析文件名中的时间信息
    print(f"\n7. 文件名时间分析:")
    filename = "0053_20250723_202435.part1.iq.wav"
    if "20250723_202435" in filename:
        try:
            file_time = datetime.strptime("20250723_202435", "%Y%m%d_%H%M%S")
            print(f"   文件名时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
            file_timestamp = file_time.timestamp()
            print(f"   文件名对应的Unix时间戳: {file_timestamp}")
            
            # 计算与文件时间的差值
            diff = timestamp - file_timestamp
            print(f"   与文件时间的差值: {diff}")
            print(f"   差值（秒）: {diff}")
            print(f"   差值（毫秒）: {diff / 1000}")
            print(f"   差值（微秒）: {diff / 1000000}")
        except Exception as e:
            print(f"   文件名时间解析失败: {e}")

def analyze_binary_representation(timestamp):
    """
    分析时间戳的二进制表示
    """
    print(f"\n二进制表示分析:")
    print("-" * 30)
    
    # 转换为整数
    timestamp_int = int(timestamp)
    
    # 二进制表示
    binary = bin(timestamp_int)
    print(f"二进制: {binary}")
    print(f"二进制长度: {len(binary) - 2} 位")  # 减去'0b'前缀
    
    # 十六进制表示
    hex_repr = hex(timestamp_int)
    print(f"十六进制: {hex_repr}")
    
    # 分析可能的字段
    print(f"\n可能的字段分解:")
    # 假设是64位时间戳，尝试分解
    if timestamp_int.bit_length() <= 64:
        # 高32位和低32位
        high_32 = (timestamp_int >> 32) & 0xFFFFFFFF
        low_32 = timestamp_int & 0xFFFFFFFF
        print(f"高32位: {high_32} (0x{high_32:08X})")
        print(f"低32位: {low_32} (0x{low_32:08X})")
        
        # 尝试将高32位作为秒，低32位作为分数秒
        try:
            dt_combined = datetime.fromtimestamp(high_32 + low_32 / (2**32), tz=timezone.utc)
            print(f"组合时间（高32位秒+低32位分数）: {dt_combined.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        except:
            pass

def main():
    """主函数"""
    print("=" * 60)
    print("时间戳格式分析")
    print("=" * 60)
    
    # 从GNSS数据中获取时间戳
    timestamp = 990956329492035.0
    
    analyze_timestamp(timestamp)
    analyze_binary_representation(timestamp)
    
    print(f"\n" + "=" * 60)
    print("结论和建议:")
    print("=" * 60)
    print("1. 这个时间戳值非常大，不是标准的Unix时间戳")
    print("2. 可能是设备特定的高精度计时器")
    print("3. 建议查看设备文档或联系设备厂商获取时间戳格式说明")
    print("4. 如果需要绝对时间，可能需要结合文件名中的时间信息")

if __name__ == "__main__":
    main()
