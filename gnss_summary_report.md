# IQS WAV文件GNSS信息提取报告

## 文件信息
- **文件名**: `0053_20250723_202435.part1.iq.wav`
- **文件路径**: `./0053_20250723_202435.part1.iq.wav`
- **数据包数量**: 2个

## GNSS信息提取结果

### 位置信息（经纬度）
- **纬度**: 28.220921° N
- **经度**: 112.991798° E
- **位置精度**: 6位小数（约1米精度）
- **位置状态**: 静止（所有数据包位置相同）

### 地理位置分析
根据经纬度坐标 (28.220921°N, 112.991798°E)，该位置位于：
- **国家**: 中国
- **大致区域**: 湖南省长沙市附近
- **坐标系**: 可能是WGS84坐标系

### 时间信息
- **原始时间戳**: 990956329492035.0
- **时间戳格式**: 可能是微秒级时间戳
- **转换后时间**: 2001-05-27 09:38:49.492035 UTC
- **文件名时间**: 2025-07-23 20:24:35（来自文件名）

### 数据包详情

| 包号 | 时间戳 | 纬度 | 经度 | 系统计时器 |
|------|--------|------|------|------------|
| 0 | 990956329492035.0 | 28.220921 | 112.991798 | 0.0 |
| 1 | 990956329492035.0 | 28.220921 | 112.991798 | 0.0 |

## 技术细节

### 数据提取方法
根据`iqs记录文件说明.docx`文档，GNSS信息存储在WAV文件的`trig chunk`中：

1. **AbsoluteTimeStamp** (8字节, double类型, 大端序)
   - 偏移位置: DeviceState结构体内
   - 数据类型: 64位双精度浮点数
   - 字节序: 大端序

2. **Latitude** (4字节, float类型, 大端序)
   - 偏移位置: AbsoluteTimeStamp之后
   - 数据类型: 32位单精度浮点数
   - 字节序: 大端序

3. **Longitude** (4字节, float类型, 大端序)
   - 偏移位置: Latitude之后
   - 数据类型: 32位单精度浮点数
   - 字节序: 大端序

### 字节序转换
由于数据以大端序存储，需要进行字节序转换：
```python
def convert_endian_float(value_bytes):
    val_int = struct.unpack('<I', value_bytes)[0]
    val_int_swapped = swap_32(val_int)
    return struct.unpack('!f', struct.pack('!I', val_int_swapped))[0]

def convert_endian_double(value_bytes):
    val_int = struct.unpack('<Q', value_bytes)[0]
    val_int_swapped = swap_64(val_int)
    return struct.unpack('!d', struct.pack('!Q', val_int_swapped))[0]
```

## 代码实现

### 主要函数
1. **`extract_gnss_info(wav_file_path)`**: 提取GNSS信息的主函数
2. **`get_iqs_wav_file_data()`**: 读取单个数据包的详细信息
3. **字节序转换函数**: 处理大端序数据

### 输出文件
- `./data/gnss_info.csv`: 基本GNSS信息
- `./data/gnss_detailed_analysis.csv`: 详细分析结果

## 时间戳分析

### 可能的时间戳格式
经过分析，时间戳 `990956329492035.0` 最可能的解释：

1. **微秒级时间戳** (最可能)
   - 转换结果: 2001-05-27 09:38:49.492035 UTC
   - 这是一个合理的日期时间

2. **设备特定计时器**
   - 可能是设备启动后的高精度计时器
   - 需要参考设备文档确定具体格式

### 时间戳特点
- 15位数字的大整数
- 在所有数据包中保持相同
- 不是标准Unix时间戳格式

## 结论

1. **成功提取了GNSS信息**: 经纬度数据完整且精确
2. **位置固定**: 设备在记录期间处于静止状态
3. **时间戳需要进一步确认**: 建议查阅设备文档确定确切的时间戳格式
4. **数据质量良好**: GNSS数据结构完整，符合文档规范

## 建议

1. **时间戳格式确认**: 联系设备厂商确认时间戳的确切格式
2. **坐标系确认**: 确认使用的坐标系（WGS84、GCJ02等）
3. **精度评估**: 如需高精度应用，建议评估GNSS接收器的精度规格
4. **动态数据测试**: 建议使用移动状态下的数据文件测试轨迹提取功能

## 相关文件
- `main.py`: 主要的数据提取代码
- `gnss_info_analysis.py`: 详细分析脚本
- `timestamp_analysis.py`: 时间戳格式分析
- `iqs记录文件说明.docx`: 官方文档说明
